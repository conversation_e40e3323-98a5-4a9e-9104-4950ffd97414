# DataSampler2 多周期数据采样器

## 概述

DataSampler2 是一个专门设计用于处理多周期因子特征向量的数据采样器。与原有的 DataSampler 不同，DataSampler2 将多周期的因子特征向量分开处理，不进行合并，每个周期保持独立的特征向量。

## 主要特点

### 1. 多周期分离处理
- ✅ **独立处理**: 每个周期的因子特征向量完全独立，不进行合并
- ✅ **周期识别**: 周期根据 `fd_dfs` 的键来决定（如 'fd_1_0', 'fd_1_1', 'fd_2_0'）
- ✅ **特征维度灵活**: 不同周期可以有不同数量的特征

### 2. 时间对齐
- ✅ **统一时间索引**: 所有周期使用相同的时间索引进行采样
- ✅ **跨代码检查**: 确保采样窗口不跨越不同的交易代码
- ✅ **时间特征支持**: 支持时间编码特征

### 3. 采样模式
- ✅ **常规采样**: 按步长进行规律采样
- ✅ **过滤采样**: 基于滚动窗口最大值进行过滤采样
- ✅ **极端值过滤**: 可选的极端值过滤功能

## 数据结构

### 输入数据格式

```python
# fd_dfs: 多周期因子数据字典
fd_dfs = {
    'fd_1_0': pd.DataFrame({  # 短期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'RSI_2': [...],
        'MACD_2': [...],
        'VOL_2': [...]
    }),
    'fd_1_1': pd.DataFrame({  # 中期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'MA_5_2': [...],
        'MA_10_2': [...],
        'BOLL_UP_2': [...],
        'BOLL_DOWN_2': [...]
    }),
    'fd_2_0': pd.DataFrame({  # 长期因子
        'code': [...],
        'date': [...],
        'change': [...],
        'MA_20_2': [...],
        'ATR_2': [...]
    })
}

# lb_df: 标签数据框
lb_df = pd.DataFrame({
    'code': [...],
    'date': [...],
    'change': [...],
    'long_label': [...],
    'short_label': [...],
    'label': [...],
    'code_encoded': [...],
    'tf0': [...],  # 时间特征
    'tf1': [...],
    'tf2': [...],
    'tf3': [...],
    'tf4': [...]
})
```

### 输出数据格式

```python
# 返回按周期分组的采样结果字典
results = {
    'fd_1_0': (code_data, x_data, y_data, [x_mark, y_mark]),
    'fd_1_1': (code_data, x_data, y_data, [x_mark, y_mark]),
    'fd_2_0': (code_data, x_data, y_data, [x_mark, y_mark])
}

# 每个周期的数据形状示例:
# fd_1_0: x_data.shape = (样本数, 窗口大小, 特征数量)
# fd_1_1: x_data.shape = (样本数, 窗口大小, 不同的特征数量)
# fd_2_0: x_data.shape = (样本数, 窗口大小, 又不同的特征数量)
```

## 使用方法

### 基本使用

```python
from pyqlab.data.dataset.handler import DataSampler2, DataConfig, DirectionType

# 1. 创建配置
config = DataConfig(
    win=10,                    # 窗口大小
    step=1,                    # 采样步长
    is_filter_extreme=False,   # 极端值过滤
    is_normal=False,           # 数据归一化
    verbose=True,              # 详细日志
    timeenc=0,                 # 时间编码
    extreme_threshold=3.0      # 极端值阈值
)

# 2. 创建采样器
sampler = DataSampler2(config)

# 3. 进行采样
results = sampler.sample_data(
    fd_dfs=fd_dfs,                    # 多周期因子数据
    lb_df=lb_df,                      # 标签数据
    direction=DirectionType.LONG_SHORT, # 交易方向
    win=10,                           # 窗口大小
    filter_win=0                      # 过滤窗口（0表示常规采样）
)

# 4. 处理结果
for period_name, result in results.items():
    code_data, x_data, y_data = result[0], result[1], result[2]
    print(f"{period_name}: 样本数={len(x_data)}, 特征维度={x_data.shape}")
```

### 过滤采样

```python
# 使用过滤窗口进行采样
results_filtered = sampler.sample_data(
    fd_dfs=fd_dfs,
    lb_df=lb_df,
    direction=DirectionType.MULTI_LABEL,
    win=10,
    filter_win=5  # 使用5个时间步的过滤窗口
)
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `win` | int | 10 | 采样窗口大小 |
| `step` | int | 1 | 采样步长 |
| `is_filter_extreme` | bool | False | 是否过滤极端值 |
| `is_normal` | bool | True | 是否进行数据归一化 |
| `verbose` | bool | False | 是否输出详细日志 |
| `timeenc` | int | None | 时间编码类型 |
| `extreme_threshold` | float | 3.0 | 极端值过滤阈值 |

## 交易方向类型

| 方向 | 枚举值 | 说明 |
|------|--------|------|
| 多头 | `DirectionType.LONG` | 使用 long_label |
| 空头 | `DirectionType.SHORT` | 使用 short_label |
| 多空 | `DirectionType.LONG_SHORT` | 使用 change 值 |
| 多分类 | `DirectionType.MULTI_LABEL` | 使用 label |

## 实际应用建议

### 1. 周期特征设计
- **短期周期 (fd_1_0)**: 技术指标、价格动量、成交量比率等
- **中期周期 (fd_1_1)**: 移动平均、布林带、趋势指标等  
- **长期周期 (fd_2_0)**: 长期移动平均、波动率、宏观指标等

### 2. 模型架构建议
```python
# 在深度学习模型中，可以为不同周期设计不同的网络分支
class MultiPeriodModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.short_term_branch = nn.LSTM(4, 64)   # fd_1_0: 4个特征
        self.medium_term_branch = nn.LSTM(6, 64)  # fd_1_1: 6个特征  
        self.long_term_branch = nn.LSTM(3, 64)    # fd_2_0: 3个特征
        self.fusion_layer = nn.Linear(192, 1)
    
    def forward(self, period_data):
        short_out = self.short_term_branch(period_data['fd_1_0'])
        medium_out = self.medium_term_branch(period_data['fd_1_1'])
        long_out = self.long_term_branch(period_data['fd_2_0'])
        
        combined = torch.cat([short_out, medium_out, long_out], dim=-1)
        return self.fusion_layer(combined)
```

### 3. 数据质量检查
- 确保所有周期的数据在时间上对齐
- 检查不同周期的特征数量和类型
- 验证标签数据的完整性

## 与原 DataSampler 的区别

| 特性 | DataSampler | DataSampler2 |
|------|-------------|--------------|
| 数据输入 | 合并后的特征矩阵 | 多周期分离的数据字典 |
| 特征处理 | 所有特征合并为一个矩阵 | 每个周期独立的特征矩阵 |
| 输出格式 | 单一的特征张量 | 按周期分组的特征字典 |
| 灵活性 | 固定的特征维度 | 每个周期可有不同特征数 |
| 适用场景 | 传统机器学习模型 | 多分支深度学习模型 |

## 测试和验证

项目包含完整的测试文件：
- `test_data_sampler2.py`: 基础功能测试
- `example_data_sampler2_usage.py`: 使用示例

运行测试：
```bash
python test_data_sampler2.py
python example_data_sampler2_usage.py
```

## 总结

DataSampler2 为多周期因子数据处理提供了更灵活和强大的解决方案，特别适合需要保持不同周期特征独立性的深度学习模型。通过分离处理不同周期的数据，可以更好地捕捉各个时间尺度上的市场特征。
